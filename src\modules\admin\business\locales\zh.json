{"admin": {"business": {"product": {"title": "产品", "createProduct": "创建产品", "editProduct": "编辑产品", "productList": "产品列表", "productInfo": "产品信息", "productAttributes": "产品属性", "productImages": "产品图片", "name": "产品名称", "tags": "标签", "createdBy": "创建者", "typeprice": "价格类型", "detail": "产品详情", "notFound": "未找到产品", "basicInfo": "基本信息", "priceInfo": "价格信息", "description": "产品描述", "shipmentConfig": "运输配置", "width": "宽度", "height": "高度", "length": "长度", "weight": "重量", "noImages": "暂无图片", "noTags": "暂无标签", "priceType": {"title": "价格类型", "hasPrice": "固定价格", "stringPrice": "描述性价格", "noPrice": "无价格"}, "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组"}, "listPrice": "标价", "salePrice": "销售价", "currency": "货币", "priceDescription": "价格描述", "createSuccess": "产品创建成功", "createError": "创建产品时出错", "updateSuccess": "产品更新成功", "updateError": "更新产品时出错", "deleteSuccess": "产品删除成功", "deleteError": "删除产品时出错", "bulkDeleteSuccess": "成功删除 {{count}} 个产品", "bulkDeleteError": "批量删除产品时出错", "selectToDelete": "请至少选择一个产品进行删除", "confirmDeleteMessage": "您确定要删除此产品吗？", "confirmBulkDeleteMessage": "您确定要删除选定的 {{count}} 个产品吗？", "fields": {"name": "产品名称", "price": "价格", "priceType": "价格类型", "priceTypes": {"yes": "是", "no": "否", "other": "其他"}}, "form": {"title": "添加新产品", "name": "产品名称", "description": "描述", "price": "价格", "category": "类别", "sku": "SKU", "status": "状态", "inventory": "库存", "submit": "保存产品", "cancel": "取消", "createTitle": "添加新产品", "editTitle": "编辑产品", "namePlaceholder": "输入产品名称", "descriptionPlaceholder": "输入产品描述", "pricePlaceholder": "输入产品价格", "categoryPlaceholder": "选择产品类别", "skuPlaceholder": "输入产品SKU", "statusPlaceholder": "选择产品状态", "inventoryPlaceholder": "输入产品库存", "tagsPlaceholder": "输入产品标签", "mediaPlaceholder": "拖放或点击上传产品图片", "media": "产品图片", "shipmentConfig": {"title": "运输配置", "widthCm": "宽度 (cm)", "heightCm": "高度 (cm)", "lengthCm": "长度 (cm)"}, "customFields": {"title": "自定义字段", "selectField": "选择自定义字段", "selectGroupForm": "选择自定义字段组", "searchPlaceholder": "搜索自定义字段...", "searchGroupPlaceholder": "搜索自定义字段组...", "selectedFields": "已选自定义字段", "selectedGroupForm": "已选自定义字段组", "addField": "添加自定义字段", "addGroupForm": "添加自定义字段组"}, "variants": {"title": "产品分类", "addVariant": "添加分类", "variant": "分类", "noVariants": "暂无分类。点击\"添加分类\"开始。", "customFields": "分类属性", "searchCustomField": "搜索属性"}, "priceDescriptionPlaceholder": "输入价格描述", "priceTypePlaceholder": "选择价格类型", "priceTypes": {"yes": "是", "no": "否", "other": "其他"}}, "productDetails": {"regularPrice": "常规价格", "salePrice": "促销价格", "priceNote": "价格备注", "brand": "品牌", "url": "网址", "description": "描述", "attributes": "属性", "attributeName": "属性名称", "attributeType": "数据类型", "attributeValue": "默认值"}, "validation": {"nameRequired": "请输入产品名称", "attributeNameRequired": "请输入属性名称", "attributeTypeRequired": "请选择数据类型"}, "attributeTypes": {"text": "文本", "number": "数字", "date": "日期", "boolean": "是/否", "list": "列表"}, "images": {"addImages": "添加产品图片", "image": "图片", "url": "网址", "video": "视频", "uploadImage": "上传图片", "enterImageUrl": "输入图片网址", "enterVideoUrl": "输入视频网址", "recommendedSize": "建议尺寸：800x600像素，最大2MB", "addToList": "添加到列表", "uploadedImages": "已上传图片", "urlImages": "网址图片", "videoList": "视频列表", "setCover": "设为封面", "coverImage": "封面图片", "uploadedFromComputer": "从电脑上传", "dragAndDrop": "拖放或点击上传产品图片"}, "actions": {"createProduct": "创建产品", "saveProduct": "保存产品", "view": "查看", "deleteProduct": "删除产品", "cancelCreation": "取消"}, "messages": {"productCreated": "产品创建成功", "productUpdated": "产品更新成功", "productDeleted": "产品删除成功", "confirmDelete": "您确定要删除此产品吗？"}, "statusTypes": {"APPROVED": "已批准", "PENDING": "待审批", "REJECTED": "已拒绝"}, "viewForm": {"status": {"active": "活跃", "inactive": "不活跃"}, "priceTypes": {"yes": "有价格", "no": "免费", "other": "联系"}, "imagesTitle": "产品图片", "noDescription": "无描述", "createdAt": "创建时间", "updatedAt": "更新时间", "close": "关闭", "view": "查看"}, "filters": {"all": "全部"}, "table": {"image": "图片", "name": "产品名称", "price": "销售价", "typePrice": "价格类型", "status": "状态", "actions": "操作", "moreActions": "更多操作", "selectedItems": "已选择 {{count}} 项", "clearSelection": "清除全部"}, "update": "更新", "loadError": "加载产品数据时出错", "bulkUpdateStatus": "批量更新状态", "bulkUpdateSuccess": "状态更新成功", "bulkUpdateSuccessMessage": "已为 {{count}} 个产品更新状态", "bulkUpdateError": "状态更新错误", "bulkUpdateErrorMessage": "无法更新产品状态。请重试。", "confirmBulkStatusUpdateMessage": "为 {{count}} 个选中的产品选择新状态：", "selectStatus": "选择状态", "rejectReason": "拒绝原因", "rejectReasonPlaceholder": "输入拒绝原因...", "status": {"status": "状态", "PENDING": "待审批", "APPROVED": "已批准", "REJECTED": "已拒绝", "ACTIVE": "活跃", "INACTIVE": "不活跃"}}, "customField": {"title": "自定义字段", "description": "管理自定义字段", "maxLength": "最大长度", "component": "组件类型", "components": {"input": "输入框", "textarea": "文本区域", "select": "下拉选择", "checkbox": "复选框", "radio": "单选按钮", "date": "日期", "number": "数字", "file": "文件", "multiSelect": "多选"}, "type": "数据类型", "configId": "配置 ID", "types": {"text": "文本", "number": "数字", "boolean": "是/否", "date": "日期", "select": "选择框", "object": "对象", "array": "数组", "string": "字符串"}, "name": "字段名称", "label": "标签", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项", "value": "值", "validation": {"minLength": "最小长度", "maxLength": "最大长度", "pattern": "模式", "min": "最小值", "max": "最大值"}, "booleanValues": {"true": "是", "false": "否"}, "patterns": {"email": "邮箱", "phoneVN": "越南电话号码", "phoneIntl": "国际电话号码", "postalCodeVN": "越南邮政编码", "lettersOnly": "仅字母", "numbersOnly": "仅数字", "alphanumeric": "字母和数字", "noSpecialChars": "无特殊字符", "url": "网址", "ipv4": "IPv4地址", "strongPassword": "强密码", "vietnameseName": "越南姓名", "studentId": "学生证号", "nationalId": "身份证号", "taxCode": "税号", "dateFormat": "日期 (dd/mm/yyyy)", "timeFormat": "时间 (hh:mm)", "hexColor": "十六进制颜色", "base64": "Base64编码", "uuid": "UUID", "filename": "文件名", "urlSlug": "URL别名", "variableName": "变量名", "creditCard": "信用卡号", "qrCode": "二维码", "gpsCoordinate": "GPS坐标", "rgbColor": "RGB颜色", "domain": "域名", "decimal": "小数", "barcode": "条形码"}, "confirmDeleteMessage": "您确定要删除此自定义字段吗？", "table": {"name": "字段名称", "component": "组件", "type": "类型", "required": "必填", "createdAt": "创建时间", "status": "状态", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部"}, "actions": {"view": "查看", "edit": "编辑", "delete": "删除"}, "status": {"status": "状态", "active": "活跃", "pending": "待处理", "inactive": "不活跃"}, "required": {"required": "必填", "yes": "是", "no": "否"}, "confirmDelete": {"title": "确认删除"}, "detail": "自定义字段详情", "notFound": "未找到自定义字段", "configJson": "配置JSON", "noConfigJson": "无配置JSON", "linkedGroups": "关联组", "createdAt": "创建时间", "close": "关闭", "add": "添加", "edit": "编辑", "save": "保存", "cancel": "取消", "form": {"componentRequired": "请选择组件类型", "labelRequired": "请输入标签", "typeRequired": "请选择数据类型", "idRequired": "请输入字段标识符名称", "configIdRequired": "配置ID是必需的", "configIdPlaceholder": "输入配置ID", "labelPlaceholder": "输入显示标签", "descriptionPlaceholder": "输入字段描述", "placeholderPlaceholder": "输入占位符文本", "defaultValuePlaceholder": "输入默认值", "optionsPlaceholder": "输入选项，逗号分隔或JSON格式", "description": "描述", "labelTagRequired": "请至少添加一个标签", "fieldIdLabel": "字段标识符名称", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "显示名称", "displayNamePlaceholder": "输入此字段的显示名称", "displayNameRequired": "请输入显示名称", "labelInputPlaceholder": "输入标签并按回车", "tagsCount": "个标签已添加", "patternSuggestions": "常用模式建议：", "selectOptionsPlaceholder": "按Name|Value格式输入值，每行一对。示例：\na|1\nb|2", "booleanDefaultPlaceholder": "选择默认值", "dateDefaultPlaceholder": "选择默认日期", "showAdvancedSettings": "显示高级设置", "placeholder": "占位符", "defaultValue": "默认值", "options": "选项"}, "createSuccess": "自定义字段创建成功", "createError": "创建自定义字段时出错", "updateSuccess": "自定义字段更新成功", "updateError": "更新自定义字段时出错", "deleteSuccess": "自定义字段删除成功", "deleteError": "删除自定义字段时出错", "loadError": "加载自定义字段时出错"}, "customGroupForm": {"title": "自定义字段组", "description": "管理自定义字段组", "createSuccess": "自定义字段组创建成功", "createError": "创建自定义字段组时出错", "updateSuccess": "自定义字段组更新成功", "updateError": "更新自定义字段组时出错", "deleteSuccess": "自定义字段组删除成功", "deleteError": "删除自定义字段组时出错", "loadError": "加载自定义字段组时出错"}, "inventory": {"title": "库存管理", "description": "管理库存和出入库", "totalItems": "总商品数", "manage": "管理库存", "status": {"inStock": "有库存", "lowStock": "库存不足", "outOfStock": "无库存"}}, "conversion": {"title": "转化", "description": "跟踪和管理转化", "totalConversions": "总转化数", "manage": "管理转化", "id": "ID", "customerId": "客户ID", "userId": "用户ID", "type": "转化类型", "name": "名称", "source": "来源", "destination": "目的地", "value": "价值", "date": "日期", "status": {"completed": "已完成", "pending": "处理中", "failed": "失败"}, "detail": "转化详情", "conversionInfo": "转化信息", "customerInfo": "客户信息", "conversionType": "转化类型", "notes": "备注", "content": "内容", "campaign": "活动", "avatar": "头像", "email": "邮箱", "phone": "电话", "platform": "平台", "timezone": "时区", "agentId": "代理ID", "metadata": "元数据", "tags": "标签", "primaryEmail": "主邮箱", "secondaryEmail": "副邮箱", "notFound": "未找到转化信息", "table": {"id": "ID", "customerId": "客户ID", "userId": "用户ID", "type": "转化类型", "source": "来源", "createdAt": "创建时间", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部"}, "actions": {"view": "查看"}, "createdAt": "创建时间", "updatedAt": "更新时间", "close": "关闭"}, "order": {"title": "订单", "description": "管理订单", "createOrder": "创建新订单", "editOrder": "编辑订单", "viewOrder": "查看订单详情", "orderNumber": "订单号", "customerInfo": "客户信息", "customerName": "客户姓名", "customerEmail": "电子邮件", "customerPhone": "电话", "customerAddress": "地址", "items": "订单商品", "noItems": "此订单没有商品", "quantity": "数量", "totalAmount": "总金额", "status": {"title": "状态", "pending": "待处理", "processing": "处理中", "completed": "已完成", "cancelled": "已取消", "refunded": "已退款"}, "paymentMethod": "支付方式", "paymentMethods": {"cash": "现金", "creditCard": "信用卡", "bankTransfer": "银行转账", "digitalWallet": "电子钱包"}, "paymentStatus": {"title": "支付状态", "paid": "已支付", "unpaid": "未支付", "partiallyPaid": "部分支付"}, "notes": "备注", "form": {"customerNamePlaceholder": "输入客户姓名", "customerEmailPlaceholder": "输入客户电子邮件", "customerPhonePlaceholder": "输入客户电话", "customerAddressPlaceholder": "输入客户地址", "notesPlaceholder": "输入订单备注"}, "createSuccess": "订单创建成功", "createError": "创建订单时出错", "updateSuccess": "订单更新成功", "updateError": "更新订单时出错", "deleteSuccess": "订单删除成功", "deleteError": "删除订单时出错", "confirmDeleteMessage": "您确定要删除此订单吗？", "table": {"orderNumber": "订单号", "customer": "客户", "totalAmount": "总金额", "carrier": "承运商", "orderDate": "订单日期", "status": "状态", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部"}, "actions": {"view": "查看"}, "detail": "订单详情", "notFound": "未找到订单", "source": "来源", "shippingStatus": "配送状态", "hasShipping": "有配送", "yes": "是", "no": "否", "productInfo": "产品信息", "billInfo": "账单信息", "subtotal": "小计", "total": "总计", "logisticInfo": "物流信息", "carrier": "承运商", "address": "地址", "notSet": "未设置", "createdAt": "创建时间", "updatedAt": "更新时间", "close": "关闭", "shipped": "已发货", "delivered": "已送达"}, "routes": {"business": "业务管理 - 管理员", "product": "产品管理 - 管理员", "conversion": "转换管理 - 管理员", "order": "订单管理 - 管理员", "warehouse": "仓库管理 - 管理员", "virtualWarehouse": "虚拟仓库管理 - 管理员", "customField": "自定义字段管理 - 管理员", "warehouseCustomField": "仓库自定义字段管理 - 管理员", "file": "文件管理 - 管理员", "folder": "文件夹管理 - 管理员"}, "businessPage": {"modules": {"product": {"title": "产品管理", "description": "管理和跟踪用户产品"}, "conversion": {"title": "转换管理", "description": "跟踪和管理转换记录"}, "order": {"title": "订单管理", "description": "管理和跟踪用户订单"}, "warehouse": {"title": "仓库管理", "description": "管理实体和虚拟仓库"}, "virtualWarehouse": {"title": "虚拟仓库管理", "description": "管理虚拟仓库和系统集成"}, "customField": {"title": "自定义字段", "description": "管理系统自定义字段"}, "warehouseCustomField": {"title": "仓库自定义字段", "description": "管理仓库自定义字段"}, "file": {"title": "文件管理", "description": "管理系统文件和文档"}, "folder": {"title": "文件夹管理", "description": "管理系统文件夹结构"}}}, "virtualWarehouse": {"title": "虚拟仓库", "description": "管理虚拟仓库", "manage": "管理虚拟仓库", "warehouseId": "仓库ID", "associatedSystem": "关联系统", "purpose": "用途", "warehouse": "仓库信息", "customFields": "自定义字段", "name": "仓库名称", "type": "仓库类型", "status": "状态", "createSuccess": "虚拟仓库创建成功", "createError": "创建虚拟仓库时出错", "updateSuccess": "虚拟仓库更新成功", "updateError": "更新虚拟仓库时出错", "deleteSuccess": "虚拟仓库删除成功", "deleteError": "删除虚拟仓库时出错", "confirmDeleteMessage": "您确定要删除此虚拟仓库吗？", "notFound": "未找到虚拟仓库", "detail": "虚拟仓库详情", "basicInfo": "基本信息", "warehouseInfo": "仓库信息", "customFieldsInfo": "自定义字段", "noCustomFields": "无自定义字段", "noAssociatedSystem": "无关联系统", "noPurpose": "无指定用途", "close": "关闭", "table": {"warehouseId": "仓库ID", "name": "仓库名称", "associatedSystem": "关联系统", "purpose": "用途", "type": "类型", "status": "状态", "createdAt": "创建时间", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部", "active": "活跃", "inactive": "不活跃"}, "actions": {"view": "查看", "edit": "编辑", "delete": "删除"}, "types": {"VIRTUAL": "虚拟", "PHYSICAL": "实体"}, "statusTypes": {"ACTIVE": "活跃", "INACTIVE": "不活跃"}}, "warehouse": {"title": "仓库", "description": "管理仓库", "name": "仓库名称", "code": "仓库代码", "desc": "描述", "type": "仓库类型", "types": {"PHYSICAL": "实体仓库", "VIRTUAL": "虚拟仓库"}, "status": "状态", "address": "地址", "contact": "联系信息", "add": "添加仓库", "edit": "编辑仓库", "addForm": "添加新仓库", "editForm": "编辑仓库信息", "createSuccess": "仓库创建成功", "updateSuccess": "仓库更新成功", "deleteSuccess": "仓库删除成功", "createError": "创建仓库时出错", "updateError": "更新仓库时出错", "deleteError": "删除仓库时出错", "confirmDeleteMessage": "您确定要删除此仓库吗？", "form": {"namePlaceholder": "输入仓库名称", "descriptionPlaceholder": "输入仓库描述", "typePlaceholder": "选择仓库类型", "selectType": "选择仓库类型"}, "detail": "仓库详情", "notFound": "未找到仓库", "basicInfo": "基本信息", "id": "ID", "capacity": "容量", "customFields": "自定义字段", "noCustomFields": "无自定义字段", "noDescription": "无描述", "notSet": "未设置", "close": "关闭", "table": {"id": "ID", "name": "仓库名称", "description": "描述", "address": "地址", "capacity": "容量", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部", "highCapacity": "高容量", "lowCapacity": "低容量"}, "actions": {"view": "查看"}}, "warehouseCustomField": {"title": "仓库自定义字段", "description": "管理仓库自定义字段", "detail": "仓库自定义字段详情", "basicInfo": "基本信息", "fieldInfo": "字段信息", "currentValue": "当前值", "warehouseId": "仓库ID", "fieldId": "字段ID", "warehouseName": "仓库名称", "fieldLabel": "字段标签", "notFound": "未找到仓库自定义字段", "bulkUpdateStatus": "更新状态", "yes": "是", "no": "否", "notSet": "未设置", "close": "关闭", "loadError": "加载仓库自定义字段数据时出错", "table": {"id": "ID", "name": "字段名称", "label": "字段标签", "value": "值", "warehouseId": "仓库ID", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "所有仓库", "text": "文本字段", "number": "数字字段", "boolean": "布尔字段", "date": "日期字段"}, "actions": {"view": "查看"}}, "file": {"detail": "文件详情", "notFound": "未找到文件", "basicInfo": "基本信息", "name": "文件名", "originalName": "原始名称", "type": "文件类型", "size": "大小", "extension": "扩展名", "mimeType": "MIME类型", "folderInfo": "文件夹信息", "urls": "链接", "url": "URL", "thumbnailUrl": "缩略图URL", "metadata": "元数据", "noMetadata": "无元数据", "storageKey": "存储键", "types": {"image": "图片", "video": "视频", "document": "文档", "audio": "音频", "other": "其他"}, "status": {"active": "活跃", "inactive": "不活跃"}, "table": {"name": "文件名", "size": "大小", "folder": "文件夹", "createdAt": "创建时间", "actions": "操作", "moreActions": "更多操作"}, "actions": {"view": "查看"}, "filters": {"all": "全部", "image": "图片", "video": "视频", "document": "文档", "audio": "音频", "other": "其他"}, "loadError": "加载文件数据时出错", "notSet": "未设置"}, "folder": {"detail": "文件夹详情", "notFound": "未找到文件夹", "basicInfo": "基本信息", "name": "文件夹名称", "parentId": "父级ID", "description": "描述", "pathInfo": "路径信息", "path": "路径", "breadcrumbs": "完整路径", "noBreadcrumbs": "根文件夹", "parentInfo": "父文件夹信息", "parentName": "父级名称", "parentPath": "父级路径", "ownerInfo": "所有者信息", "ownerId": "所有者ID", "ownerName": "所有者名称", "ownerEmail": "所有者邮箱", "fileCount": "文件数量", "subFolderCount": "子文件夹数量", "files": "个文件", "subFolders": "个子文件夹", "root": "根目录", "filesList": "文件列表", "filesInFolder": "此文件夹中的文件将在这里显示", "status": {"active": "活跃", "inactive": "不活跃"}, "table": {"name": "文件夹名称", "parent": "父文件夹", "path": "路径", "owner": "所有者", "createdAt": "创建时间", "actions": "操作", "moreActions": "更多操作"}, "filters": {"all": "全部", "active": "活跃", "inactive": "不活跃"}, "actions": {"view": "查看"}, "rootFolder": "根文件夹", "unknownOwner": "未知", "loadError": "加载文件夹数据时出错"}, "common": {"save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "create": "创建", "back": "返回", "next": "下一步", "submit": "提交", "search": "搜索", "filter": "筛选", "sort": "排序", "add": "添加", "remove": "移除", "upload": "上传", "download": "下载", "view": "查看", "details": "详情", "actions": "操作", "active": "活跃", "inactive": "不活跃", "all": "全部", "enter": "输入", "select": "选择", "and": "和", "pressEnter": "按回车", "yes": "是", "no": "否", "close": "关闭", "name": "名称", "createdAt": "创建时间", "updatedAt": "更新时间", "noDescription": "无描述", "notSet": "未设置", "selectedItems": "已选择 {{count}} 项", "clearSelection": "清除全部", "update": "更新", "timeInfo": "时间信息", "status": {"active": "活跃", "inactive": "不活跃", "pending": "待处理"}}}}}