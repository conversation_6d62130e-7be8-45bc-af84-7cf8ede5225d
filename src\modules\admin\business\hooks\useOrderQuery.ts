import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { OrderService, OrderQueryParams, OrderStatus } from '../services/order.service';

/**
 * Query keys cho order API
 */
export const ORDER_QUERY_KEYS = {
  all: ['admin', 'business', 'orders'] as const,
  list: (params: OrderQueryParams) => [...ORDER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...ORDER_QUERY_KEYS.all, 'detail', id] as const,
  stats: ['admin', 'business', 'orders', 'stats'] as const,
};

/**
 * Hook lấy danh sách đơn hàng
 */
export const useOrders = (params: OrderQueryParams = {}) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(params),
    queryFn: () => OrderService.getOrders(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết đơn hàng theo ID
 */
export const useOrder = (id: number) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.detail(id),
    queryFn: () => OrderService.getOrderById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook để lấy thống kê đơn hàng
 */
export const useOrderStats = () => {
  return useQuery({
    queryKey: ORDER_QUERY_KEYS.stats,
    queryFn: async () => {
      // Gọi các API để lấy thống kê
      const [
        totalOrders,
        pendingOrders,
        completedOrders,
        cancelledOrders,
        totalRevenue,
        paidOrders,
        unpaidOrders
      ] = await Promise.all([
        // Tổng số đơn hàng
        OrderService.getOrders({ limit: 1 }),
        // Đơn hàng đang chờ
        OrderService.getOrders({ status: OrderStatus.PENDING, limit: 1 }),
        // Đơn hàng hoàn thành
        OrderService.getOrders({ status: OrderStatus.COMPLETED, limit: 1 }),
        // Đơn hàng đã hủy
        OrderService.getOrders({ status: OrderStatus.CANCELLED, limit: 1 }),
        // Doanh thu (từ đơn hàng hoàn thành)
        OrderService.getOrders({ status: OrderStatus.COMPLETED, limit: 1000 }),
        // Đơn hàng đã thanh toán
        OrderService.getOrders({ paymentStatus: 'PAID', limit: 1 }),
        // Đơn hàng chưa thanh toán
        OrderService.getOrders({ paymentStatus: 'UNPAID', limit: 1 }),
      ]);

      // Tính tổng doanh thu từ đơn hàng hoàn thành
      const revenue = totalRevenue.result?.items?.reduce((sum, order) => sum + order.totalAmount, 0) || 0;

      return {
        totalOrders: totalOrders.result?.meta?.totalItems || 0,
        pendingOrders: pendingOrders.result?.meta?.totalItems || 0,
        completedOrders: completedOrders.result?.meta?.totalItems || 0,
        cancelledOrders: cancelledOrders.result?.meta?.totalItems || 0,
        totalRevenue: revenue,
        paidOrders: paidOrders.result?.meta?.totalItems || 0,
        unpaidOrders: unpaidOrders.result?.meta?.totalItems || 0,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};

/**
 * Hook để xóa đơn hàng
 * @returns Mutation object
 */
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => {
      // Tạm thời return Promise.resolve cho đến khi có API thực tế
      console.log('Delete order:', id);
      return Promise.resolve();
    },
    onSuccess: () => {
      // Invalidate và refetch danh sách đơn hàng
      queryClient.invalidateQueries({ queryKey: ORDER_QUERY_KEYS.all });
    },
  });
};


