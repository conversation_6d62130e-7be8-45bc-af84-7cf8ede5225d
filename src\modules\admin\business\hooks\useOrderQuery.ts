import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { OrderService, OrderQueryParams } from '../services/order.service';

/**
 * Query keys cho order API
 */
export const ORDER_QUERY_KEYS = {
  all: ['admin', 'business', 'orders'] as const,
  list: (params: OrderQueryParams) => [...ORDER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...ORDER_QUERY_KEYS.all, 'detail', id] as const,
  stats: ['admin', 'business', 'orders', 'stats'] as const,
};

/**
 * Hook lấy danh sách đơn hàng
 */
export const useOrders = (params: OrderQueryParams = {}) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(params),
    queryFn: () => OrderService.getOrders(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết đơn hàng theo ID
 */
export const useOrder = (id: number) => {

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.detail(id),
    queryFn: () => OrderService.getOrderById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook để lấy thống kê đơn hàng
 * Vì API hiện tại không hỗ trợ filter theo status và paymentStatus,
 * chúng ta sẽ lấy tất cả đơn hàng và tính toán thống kê ở client-side
 * Thống kê dựa trên shippingStatus thay vì status theo API specification
 */
export const useOrderStats = () => {
  return useQuery({
    queryKey: ORDER_QUERY_KEYS.stats,
    queryFn: async () => {
      // Lấy tổng số đơn hàng để biết có bao nhiêu
      const totalOrdersResponse = await OrderService.getOrders({ limit: 1 });
      const totalCount = totalOrdersResponse.result?.meta?.totalItems || 0;

      if (totalCount === 0) {
        return {
          totalOrders: 0,
          pendingOrders: 0,
          completedOrders: 0,
          cancelledOrders: 0,
          shippedOrders: 0,
          totalRevenue: 0,
          paidOrders: 0,
          unpaidOrders: 0,
          withShipping: 0,
          withoutShipping: 0,
        };
      }

      // Lấy tất cả đơn hàng để tính thống kê
      // Giới hạn 1000 đơn hàng để tránh quá tải
      const limit = Math.min(totalCount, 1000);
      const allOrdersResponse = await OrderService.getOrders({ limit });
      const orders = allOrdersResponse.result?.items || [];

      // Tính toán thống kê từ dữ liệu
      let pendingOrders = 0;
      let completedOrders = 0;
      let cancelledOrders = 0;
      let shippedOrders = 0; // Thêm thống kê cho đơn hàng đã gửi
      let paidOrders = 0;
      let unpaidOrders = 0;
      let withShipping = 0;
      let withoutShipping = 0;
      let totalRevenue = 0;

      orders.forEach(order => {
        // Thống kê theo shippingStatus (theo API specification)
        if ('shippingStatus' in order) {
          switch (order.shippingStatus) {
            case 'pending':
              pendingOrders++;
              break;
            case 'completed':
              completedOrders++;
              totalRevenue += order.totalAmount;
              break;
            case 'cancelled':
              cancelledOrders++;
              break;
            case 'shipped':
              shippedOrders++;
              break;
          }
        }

        // Thống kê theo shipping (nếu có trong response)
        if ('hasShipping' in order) {
          if (order.hasShipping) {
            withShipping++;
          } else {
            withoutShipping++;
          }
        }
      });

      return {
        totalOrders: totalCount,
        pendingOrders,
        completedOrders,
        cancelledOrders,
        shippedOrders,
        totalRevenue,
        paidOrders,
        unpaidOrders,
        withShipping,
        withoutShipping,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};

/**
 * Hook để xóa đơn hàng
 * @returns Mutation object
 */
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => {
      // Tạm thời return Promise.resolve cho đến khi có API thực tế
      console.log('Delete order:', id);
      return Promise.resolve();
    },
    onSuccess: () => {
      // Invalidate và refetch danh sách đơn hàng
      queryClient.invalidateQueries({ queryKey: ORDER_QUERY_KEYS.all });
    },
  });
};


