import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CustomFieldService, CustomFieldQueryParams, CreateCustomFieldData, UpdateCustomFieldData } from '../services/custom-field.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';
import { ApiResponseDto } from '@/shared/types/api-response.interface';

/**
 * Query keys cho custom field API
 */
export const CUSTOM_FIELD_QUERY_KEYS = {
  all: ['admin', 'business', 'custom-fields'] as const,
  list: (params: CustomFieldQueryParams) => [...CUSTOM_FIELD_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOM_FIELD_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách trường tùy chỉnh
 */
export const useCustomFields = (params: CustomFieldQueryParams = {}) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: () => CustomFieldService.getCustomFields(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết trường tùy chỉnh theo ID
 */
export const useCustomField = (id: number) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => CustomFieldService.getCustomFieldById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo trường tùy chỉnh mới
 */
export const useCreateCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateCustomFieldData) => CustomFieldService.createCustomField(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:customField.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.createError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook cập nhật trường tùy chỉnh
 */
export const useUpdateCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomFieldData }) =>
      CustomFieldService.updateCustomField(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:customField.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:customField.updateError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Interface cho request body của batch delete API
 */
export interface BatchDeleteCustomFieldRequest {
  ids: number[];
}

/**
 * Interface cho kết quả xóa từng item
 */
export interface BatchDeleteResult {
  id: number;
  success: boolean;
  message: string;
}

/**
 * Interface cho response của batch delete API
 */
export interface BatchDeleteCustomFieldResponse {
  successCount: number;
  failureCount: number;
  totalCount: number;
  results: BatchDeleteResult[];
}

/**
 * Hook xóa nhiều trường tùy chỉnh (batch delete)
 */
export const useDeleteCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (ids: number | number[]) => {
      // Hỗ trợ cả xóa 1 item và nhiều items
      const idsArray = Array.isArray(ids) ? ids : [ids];
      return CustomFieldService.batchDeleteCustomFields({ ids: idsArray });
    },
    onSuccess: (response: ApiResponseDto<BatchDeleteCustomFieldResponse>, variables) => {
      const result = response.result;
      const idsArray = Array.isArray(variables) ? variables : [variables];

      if (result) {
        // Hiển thị thông báo dựa trên kết quả
        if (result.failureCount === 0) {
          // Tất cả đều thành công
          NotificationUtil.success({
            message: idsArray.length === 1
              ? t('business:customField.deleteSuccess')
              : t('business:customField.batchDeleteSuccess', {
                  count: result.successCount
                }),
            duration: 3000,
          });
        } else if (result.successCount === 0) {
          // Tất cả đều thất bại
          NotificationUtil.error({
            message: idsArray.length === 1
              ? t('business:customField.deleteError')
              : t('business:customField.batchDeleteError'),
            duration: 3000,
          });
        } else {
          // Một phần thành công, một phần thất bại
          NotificationUtil.warning({
            message: t('business:customField.batchDeletePartialSuccess', {
              successCount: result.successCount,
              failureCount: result.failureCount,
              totalCount: result.totalCount
            }),
            duration: 5000,
          });
        }
      } else {
        // Fallback nếu không có result
        NotificationUtil.success({
          message: t('business:customField.deleteSuccess'),
          duration: 3000,
        });
      }

      // Invalidate queries để refresh data
      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>, variables) => {
      const idsArray = Array.isArray(variables) ? variables : [variables];

      NotificationUtil.error({
        message: error.response?.data?.message ||
          (idsArray.length === 1
            ? t('business:customField.deleteError')
            : t('business:customField.batchDeleteError')
          ),
        duration: 3000,
      });
    },
  });
};


