import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { format } from 'date-fns';
import { vi, enUS, zhCN } from 'date-fns/locale';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useOrders, useOrderStats } from '../hooks/useOrderQuery';
import { OrderStatus, OrderListItem, OrderQueryParams } from '../services/order.service';
import ViewOrderForm from '../components/forms/ViewOrderForm';
import ListOverviewCard from '@/shared/components/widgets/ListOverviewCard/ListOverviewCard';
import { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import {
  ShoppingCart,
  Clock,
  DollarSign,
  CreditCard,
} from 'lucide-react';
/**
 * Trang quản lý đơn hàng cho Admin
 */
const OrderPage: React.FC = () => {
  const { t, i18n } = useTranslation(['admin', 'common', 'business']);

  // Helper function để lấy locale config theo ngôn ngữ hiện tại
  const getLocaleConfig = useCallback(() => {
    switch (i18n.language) {
      case 'en':
        return { locale: 'en-US', currency: 'USD', dateLocale: enUS };
      case 'zh':
        return { locale: 'zh-CN', currency: 'CNY', dateLocale: zhCN };
      default:
        return { locale: 'vi-VN', currency: 'VND', dateLocale: vi };
    }
  }, [i18n.language]);

  // State cho form và table
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null);

  // Xử lý xem chi tiết
  const handleView = useCallback((id: number) => {
    setSelectedOrderId(id);
    showForm();
  }, [setSelectedOrderId, showForm]);

  // Xử lý đóng form
  const handleCloseForm = useCallback(() => {
    setSelectedOrderId(null);
    hideForm();
  }, [setSelectedOrderId, hideForm]);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<OrderListItem>[] = useMemo(
    () => [
      {
        key: 'orderNumber',
        title: t('admin:business.order.table.orderNumber'),
        dataIndex: 'id',
        sortable: true,
      },
      {
        key: 'customerName',
        title: t('admin:business.order.table.customer'),
        dataIndex: 'userConvertCustomerId',
        sortable: true,
      },
      {
        key: 'totalAmount',
        title: t('admin:business.order.table.totalAmount'),
        dataIndex: 'billInfo.total',
        render: (value: unknown) => {
          const { locale, currency } = getLocaleConfig();
          return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency,
          }).format(value as number);
        },
        sortable: true,
      },
      {
        key: 'paymentMethod',
        title: t('admin:business.order.table.carrier'),
        dataIndex: 'logisticInfo.carrier',
        sortable: true,
      },
      {
        key: 'orderDate',
        title: t('admin:business.order.table.orderDate'),
        dataIndex: 'createdAt',
        render: (value: unknown) => {
          if (!value) return '-';
          try {
            const { dateLocale } = getLocaleConfig();
            const timestamp = value as string;
            // Kiểm tra xem timestamp có phải là số hợp lệ không
            if (/^\d+$/.test(timestamp)) {
              // Là timestamp dạng số
              const date = new Date(Number(timestamp));
              if (!isNaN(date.getTime())) {
                return format(date, 'dd/MM/yyyy HH:mm', { locale: dateLocale });
              }
            }
            // Nếu không phải timestamp dạng số, thử parse như date string
            const date = new Date(timestamp);
            if (!isNaN(date.getTime())) {
              return format(date, 'dd/MM/yyyy HH:mm', { locale: dateLocale });
            }
            return String(value);
          } catch {
            return String(value);
          }
        },
        sortable: true,
      },
      {
        key: 'status',
        title: t('admin:business.order.table.status'),
        dataIndex: 'shippingStatus',
        render: (value: unknown) => {
          const status = value as OrderStatus;
          const statusMap: Record<string, { text: string; variant: 'success' | 'warning' | 'danger' | 'default' }> = {
            [OrderStatus.PENDING]: { text: t('admin:business.order.status.pending'), variant: 'warning' },
            [OrderStatus.PREPARING]: { text: t('admin:business.order.status.processing'), variant: 'default' },
            [OrderStatus.COMPLETED]: { text: t('admin:business.order.status.completed'), variant: 'success' },
            [OrderStatus.CANCELLED]: { text: t('admin:business.order.status.cancelled'), variant: 'danger' },
            [OrderStatus.REFUNDED]: { text: t('admin:business.order.status.refunded'), variant: 'default' },
          };

          const statusInfo = statusMap[status] || { text: status, variant: 'default' as const };
          return (
            <Chip
              size="sm"
              variant={statusInfo.variant}
            >
              {statusInfo.text}
            </Chip>
          );
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('admin:business.order.table.actions'),
        render: (_, record) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('admin:business.order.actions.view'),
              icon: 'eye',
              onClick: () => handleView(Number(record.id)),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('admin:business.order.table.moreActions')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleView, getLocaleConfig]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      // Thay đổi từ status sang shippingStatus theo API specification
      if (params.filterValue === OrderStatus.PREPARING) {
        queryParams.shippingStatus = 'preparing';
      } else {
        // Các status khác có thể map sang shippingStatus tương ứng
        switch (params.filterValue) {
          case OrderStatus.PENDING:
            queryParams.shippingStatus = 'pending';
            break;
          case OrderStatus.COMPLETED:
            queryParams.shippingStatus = 'delivered';
            break;
          case OrderStatus.CANCELLED:
            queryParams.shippingStatus = 'cancelled';
            break;
          default:
            // Fallback: không gửi filter nếu không map được
            break;
        }
      }
    }

    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<OrderListItem, OrderQueryParams>({
      columns,
      filterOptions: [
        { id: 'all', label: t('admin:business.order.filters.all'), icon: 'list', value: 'all' },
        { id: 'pending', label: t('admin:business.order.status.pending'), icon: 'clock', value: OrderStatus.PENDING },
        { id: 'processing', label: t('admin:business.order.status.processing'), icon: 'loader', value: OrderStatus.PREPARING },
        { id: 'completed', label: t('admin:business.order.status.completed'), icon: 'check-circle', value: OrderStatus.COMPLETED },
        { id: 'cancelled', label: t('admin:business.order.status.cancelled'), icon: 'x-circle', value: OrderStatus.CANCELLED },
        { id: 'refunded', label: t('admin:business.order.status.refunded'), icon: 'refresh-cw', value: OrderStatus.REFUNDED },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách đơn hàng
  const { data: ordersData, isLoading } = useOrders(dataTable.queryParams);

  // Gọi API lấy thống kê đơn hàng
  const { data: statsData, isLoading: isStatsLoading } = useOrderStats();

  // Tạo dữ liệu cho các card thống kê
  const overviewCards = useMemo<OverviewCardProps[]>(() => {
    if (!statsData) return [];

    const { locale, currency } = getLocaleConfig();

    return [
      {
        title: t('admin:business.order.stats.totalOrders'),
        value: statsData.totalOrders.toLocaleString(locale),
        description: t('admin:business.order.stats.totalOrdersDesc'),
        icon: ShoppingCart,
        color: 'blue',
        isLoading: isStatsLoading,
      },
      {
        title: t('admin:business.order.stats.pendingOrders'),
        value: statsData.pendingOrders.toLocaleString(locale),
        description: t('admin:business.order.stats.pendingOrdersDesc'),
        icon: Clock,
        color: 'orange',
        isLoading: isStatsLoading,
      },
      {
        title: t('admin:business.order.stats.paidOrders'),
        value: statsData.paidOrders.toLocaleString(locale),
        description: t('admin:business.order.stats.paidOrdersDesc'),
        icon: CreditCard,
        color: 'green',
        isLoading: isStatsLoading,
      },
      {
        title: t('admin:business.order.stats.totalRevenue'),
        value: new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: currency,
          notation: 'compact',
          maximumFractionDigits: 1,
        }).format(statsData.totalRevenue),
        description: t('admin:business.order.stats.totalRevenueDesc'),
        icon: DollarSign,
        color: 'purple',
        isLoading: isStatsLoading,
      },
    ];
  }, [statsData, isStatsLoading, t, getLocaleConfig]);

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback((column: string | null, order: SortOrder | null) => {
    // Nếu column hoặc order là null, reset sort
    if (column === null || order === null) {
      dataTable.tableData.handleSortChange(null, null);
      return;
    }

    dataTable.tableData.handleSortChange(column, order as SortOrder);
  }, [dataTable.tableData]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [OrderStatus.PENDING]: t('admin:business.order.status.pending'),
      [OrderStatus.PREPARING]: t('admin:business.order.status.processing'),
      [OrderStatus.COMPLETED]: t('admin:business.order.status.completed'),
      [OrderStatus.CANCELLED]: t('admin:business.order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('admin:business.order.status.refunded'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Card thống kê tổng quan */}
      <div className="mb-6">
        <ListOverviewCard
          items={overviewCards}
          isLoading={isStatsLoading}
          skeletonCount={7}
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 4 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
        />
      </div>
      
      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedOrderId && (
          <ViewOrderForm
            id={selectedOrderId}
            onClose={handleCloseForm}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={ordersData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
         
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: ordersData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: ordersData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default OrderPage;
