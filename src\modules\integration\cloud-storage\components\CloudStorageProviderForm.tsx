import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { cloudStorageProviderConfigurationSchema } from '../schemas';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';
import { useCreateCloudStorageProvider, useUpdateCloudStorageProvider, useTestCloudStorageProviderWithConfig } from '../hooks';
import type { CloudStorageProviderConfiguration, CloudStorageProviderFormData } from '../types';

interface CloudStorageProviderFormProps {
  provider?: CloudStorageProviderConfiguration;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo/chỉnh sửa Cloud Storage Provider
 */
const CloudStorageProviderForm: React.FC<CloudStorageProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<CloudStorageProviderFormData>();

  const [formData, setFormData] = useState<CloudStorageProviderFormData>({
    providerType: provider?.providerType || 'google-drive',
    providerName: provider?.providerName || '',
    clientId: provider?.clientId || '',
    clientSecret: provider?.clientSecret || '',
    refreshToken: provider?.refreshToken || '',
    rootFolderId: provider?.rootFolderId || '',
    isActive: provider?.isActive ?? true,
    autoSync: provider?.autoSync ?? false,
    syncFolders: provider?.syncFolders ? JSON.stringify(provider.syncFolders) : '',
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const createMutation = useCreateCloudStorageProvider();
  const updateMutation = useUpdateCloudStorageProvider();

  const isEditing = !!provider;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Provider options
  const providerOptions = Object.values(CLOUD_STORAGE_PROVIDER_TYPES).map(provider => ({
    value: provider.id,
    label: provider.displayName,
  }));

  const selectedProvider = CLOUD_STORAGE_PROVIDER_TYPES[formData.providerType];

  const handleInputChange = (field: keyof CloudStorageProviderFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTestConnection = async () => {
    try {
      setIsTestingConnection(true);

      // Validate form data first
      const validatedData = cloudStorageProviderConfigurationSchema.parse(formData);

      // Parse sync folders
      if (validatedData.syncFolders) {
        try {
          JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }
    } catch (error: unknown) {
      console.error('Test connection error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = cloudStorageProviderConfigurationSchema.parse(formData);

      // Parse sync folders
      let syncFolders: string[] = [];
      if (validatedData.syncFolders) {
        try {
          syncFolders = JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const submitData = {
        providerType: validatedData.providerType,
        providerName: validatedData.providerName,
        clientId: validatedData.clientId,
        clientSecret: validatedData.clientSecret,
        refreshToken: validatedData.refreshToken,
        rootFolderId: validatedData.rootFolderId,
        isActive: validatedData.isActive,
        autoSync: validatedData.autoSync,
        syncFolders,
      };

      if (isEditing && provider) {
        await updateMutation.mutateAsync({ id: provider.id, data: submitData });
      } else {
        await createMutation.mutateAsync(submitData);
      }

      onSuccess?.();
    } catch (error: unknown) {
      console.error('Form submission error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    }
  };

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="cloud" size="lg" className="text-primary" />
          <Typography variant="h3">
            {isEditing
              ? t('integration:cloudStorage.form.editTitle')
              : t('integration:cloudStorage.form.createTitle')
            }
          </Typography>
        </div>

        <Form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
          {/* Provider Type */}
          <FormItem
            label={t('integration:cloudStorage.form.providerType.label')}
            name="providerType"
            required
          >
            <Select
              value={formData.providerType}
              onChange={(value) => handleInputChange('providerType', value as string)}
              options={providerOptions}
              placeholder={t('integration:cloudStorage.form.providerType.placeholder')}
              fullWidth
              disabled={isEditing} // Cannot change provider type when editing
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.providerType.helpText')}
            </Typography>
          </FormItem>

          {/* Provider Name */}
          <FormItem
            label={t('integration:cloudStorage.form.providerName.label')}
            name="providerName"
            required
          >
            <Input
              type="text"
              value={formData.providerName}
              onChange={(e) => handleInputChange('providerName', e.target.value)}
              placeholder={t('integration:cloudStorage.form.providerName.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.providerName.helpText')}
            </Typography>
          </FormItem>

          {/* Client ID */}
          <FormItem
            label={t('integration:cloudStorage.form.clientId.label')}
            name="clientId"
            required
          >
            <Input
              type="text"
              value={formData.clientId}
              onChange={(e) => handleInputChange('clientId', e.target.value)}
              placeholder={t('integration:cloudStorage.form.clientId.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.clientId.helpText')}
            </Typography>
          </FormItem>

          {/* Client Secret */}
          <FormItem
            label={t('integration:cloudStorage.form.clientSecret.label')}
            name="clientSecret"
            required
          >
            <Input
              type="password"
              value={formData.clientSecret}
              onChange={(e) => handleInputChange('clientSecret', e.target.value)}
              placeholder={t('integration:cloudStorage.form.clientSecret.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.clientSecret.helpText')}
            </Typography>
          </FormItem>

          {/* Refresh Token */}
          <FormItem
            label={t('integration:cloudStorage.form.refreshToken.label')}
            name="refreshToken"
            required
          >
            <Textarea
              value={formData.refreshToken}
              onChange={(e) => handleInputChange('refreshToken', e.target.value)}
              placeholder={t('integration:cloudStorage.form.refreshToken.placeholder')}
              rows={3}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.refreshToken.helpText')}
            </Typography>
          </FormItem>

          {/* Root Folder ID */}
          <FormItem
            label={t('integration:cloudStorage.form.rootFolderId.label')}
            name="rootFolderId"
          >
            <Input
              type="text"
              value={formData.rootFolderId}
              onChange={(e) => handleInputChange('rootFolderId', e.target.value)}
              placeholder={t('integration:cloudStorage.form.rootFolderId.placeholder')}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.rootFolderId.helpText')}
            </Typography>
          </FormItem>

          {/* Sync Folders */}
          <FormItem
            label={t('integration:cloudStorage.form.syncFolders.label')}
            name="syncFolders"
          >
            <Textarea
              value={formData.syncFolders}
              onChange={(e) => handleInputChange('syncFolders', e.target.value)}
              placeholder={t('integration:cloudStorage.form.syncFolders.placeholder')}
              rows={3}
              fullWidth
            />
            <Typography variant="caption" className="text-muted-foreground mt-1">
              {t('integration:cloudStorage.form.syncFolders.helpText')}
            </Typography>
          </FormItem>

          {/* Settings */}
          <div className="space-y-4">
            <Typography variant="h6">
              {t('common:settings')}
            </Typography>

            {/* Is Active */}
            <FormItem
              label={t('integration:cloudStorage.form.isActive.label')}
              name="isActive"
            >
              <Switch
                checked={formData.isActive}
                onChange={(checked) => handleInputChange('isActive', checked)}
              />
              <Typography variant="caption" className="text-muted-foreground mt-1">
                {t('integration:cloudStorage.form.isActive.helpText')}
              </Typography>
            </FormItem>

            {/* Auto Sync */}
            <FormItem
              label={t('integration:cloudStorage.form.autoSync.label')}
              name="autoSync"
            >
              <Switch
                checked={formData.autoSync}
                onChange={(checked) => handleInputChange('autoSync', checked)}
              />
              <Typography variant="caption" className="text-muted-foreground mt-1">
                {t('integration:cloudStorage.form.autoSync.helpText')}
              </Typography>
            </FormItem>
          </div>

          {/* Provider Info */}
          {selectedProvider && (
            <Card className="p-4 bg-muted/50">
              <div className="flex items-start gap-3">
                <Icon name="info" size="sm" className="text-primary mt-1" />
                <div className="space-y-2">
                  <Typography variant="subtitle2">
                    {selectedProvider.displayName}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {selectedProvider.description}
                  </Typography>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Typography variant="caption" className="text-muted-foreground">
                      <strong>{t('integration:cloudStorage.details.storageQuota')}:</strong> {selectedProvider.maxFileSize}
                    </Typography>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedProvider.supportedFeatures.map(feature => (
                      <span
                        key={feature}
                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t">
            {/* Test Connection */}
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection || isLoading}
              className="flex-1"
            >
              {isTestingConnection ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {t('common:testing')}
                </>
              ) : (
                <>
                  <Icon name="zap" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.form.testConnection')}
                </>
              )}
            </Button>

            {/* Cancel */}
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                {t('common:cancel')}
              </Button>
            )}

            {/* Submit */}
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {isEditing ? t('common:updating') : t('common:creating')}
                </>
              ) : (
                <>
                  <Icon name="save" size="sm" className="mr-2" />
                  {isEditing ? t('common:update') : t('common:create')}
                </>
              )}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CloudStorageProviderForm;
