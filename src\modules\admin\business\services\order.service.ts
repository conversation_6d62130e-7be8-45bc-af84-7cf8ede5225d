import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Enum cho trạng thái đơn hàng
 */
export enum OrderStatus {
  PENDING = 'pending',
  PREPARING = 'preparing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

/**
 * Enum cho phương thức thanh toán
 */
export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  DIGITAL_WALLET = 'DIGITAL_WALLET',
}

/**
 * Interface cho thông tin khách hàng
 */
export interface CustomerInfo {
  id: number;
  name: string;
  email: string;
  phone: string;
  address?: string;
}

/**
 * Interface cho sản phẩm trong đơn hàng
 */
export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

/**
 * Interface cho sản phẩm trong đơn hàng chi tiết
 */
export interface OrderProduct {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

/**
 * Interface cho thông tin sản phẩm trong đơn hàng
 */
export interface OrderProductInfo {
  productId: number;
  name: string;
  quantity: number;
  price: number;
  products: OrderProduct[];
}

/**
 * Interface cho thông tin hóa đơn
 */
export interface OrderBillInfo {
  subtotal: number;
  total: number;
}

/**
 * Interface cho thông tin logistics
 */
export interface OrderLogisticInfo {
  address: string;
  carrier: string;
}

/**
 * Interface cho thông tin khách hàng chi tiết
 */
export interface OrderCustomer {
  id: number;
  avatar: string;
  name: string;
  email: {
    primary: string;
  };
  phone: string;
  platform: string;
  timezone: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho chi tiết đơn hàng từ API
 */
export interface OrderDetail {
  id: number;
  userConvertCustomerId: number;
  userId: number;
  productInfo: OrderProductInfo[];
  billInfo: OrderBillInfo;
  hasShipping: boolean;
  shippingStatus: string;
  logisticInfo: OrderLogisticInfo;
  createdAt: number;
  updatedAt: number;
  source: string;
  customer: OrderCustomer;
}

/**
 * Interface cho đơn hàng (legacy)
 */
export interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  createdAt: number;
  updatedAt: number;
  notes?: string;
}

/**
 * Interface cho danh sách đơn hàng
 */
export interface OrderListItem {
  id: number;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  createdAt: number;
}

/**
 * Interface cho tham số truy vấn đơn hàng
 */
export interface OrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  fromDate?: string;
  toDate?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho dữ liệu tạo đơn hàng
 */
export interface CreateOrderData {
  customerId: number;
  items: Array<{
    productId: number;
    quantity: number;
    price: number;
  }>;
  paymentMethod: PaymentMethod;
  notes?: string;
}

/**
 * Interface cho dữ liệu cập nhật đơn hàng
 */
export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  notes?: string;
}

/**
 * Service xử lý API liên quan đến đơn hàng cho admin
 */
export const OrderService = {
  /**
   * Lấy danh sách đơn hàng
   * @param params Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  getOrders: async (params?: OrderQueryParams): Promise<ApiResponseDto<PaginatedResult<OrderListItem>>> => {
    return apiRequest.get('/admin/user-orders', { params });
  },

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  getOrderById: async (id: number): Promise<ApiResponseDto<OrderDetail>> => {
    return apiRequest.get(`/admin/user-orders/${id}`);
  },


};
