export * from './customer.types';
export * from './customer-import.types';
export * from './conversion.types';
export * from './virtual-warehouse.types';
export * from './warehouse.types';
export * from './custom-group-form.types';
export * from './order.types';
export * from './product-import.types';
export * from './report.types';
export * from './delivery.types';

// Export product types with specific exports to avoid conflicts
export {
  ProductTypeEnum,
  PriceTypeEnum,
  type HasPriceDto,
  type StringPriceDto,
  type ProductImageDto,
  type ShipmentConfigDto,
  type ClassificationDto,
  type ProductMetadataDto,
  type ProductDto,
  type ProductQueryParams,
  type PaginatedResult,
  type ProductVariantDto,
  type CreateProductDto,
  type UpdateProductDto,
  type CreateProductResponse,
} from './product.types';

// Export with alias to avoid conflict
export type {
  CustomFieldDto as ProductCustomFieldDto,
  CustomGroupFormDto as ProductCustomGroupFormDto,
} from './product.types';

// Export custom field types with explicit naming to avoid conflicts
export {
  CustomFieldComponentEnum,
  CustomFieldTypeEnum,
} from './custom-field.types';

export type {
  CustomFieldConfig,
  CustomFieldListItemDto,
  CustomFieldDetailResponseDto,
  QueryCustomFieldDto,
  QueryCustomGroupFormDto,
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  CustomGroupFormListItemDto,
  CustomGroupFormResponseDto,
} from './custom-field.types';

// Re-export with aliases to avoid conflicts
export type {
  CustomFieldDto as BusinessCustomFieldDto,
  CustomGroupFormDto as BusinessCustomGroupFormDto,
} from './custom-field.types';