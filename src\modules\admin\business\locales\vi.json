{"admin": {"business": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h do<PERSON>h", "description": "<PERSON><PERSON><PERSON><PERSON> lý các ho<PERSON>t động kinh doanh của doanh nghi<PERSON>p", "product": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách sản phẩm", "totalProducts": "Tổng số sản phẩm", "manage": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "typeprice": "Loại giá", "tags": "Thẻ", "listPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> b<PERSON>", "currency": "Đơn vị tiền tệ", "priceDescription": "<PERSON>ô tả giá", "createSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "createError": "Lỗi khi tạo sản phẩm", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "updateError": "Lỗi khi cập nh<PERSON>t sản phẩm", "deleteSuccess": "<PERSON><PERSON><PERSON> sản phẩm thành công", "deleteError": "Lỗi khi xóa sản phẩm", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> {{count}} sản phẩm thành công", "bulkDeleteError": "Lỗi khi xóa nhiều sản phẩm", "selectToDelete": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm để xóa", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa sản phẩm này?", "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} sản phẩm đã chọn?", "createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "editProduct": "Chỉnh s<PERSON>a sản phẩm", "productList": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "productDetails": "<PERSON> tiết sản phẩm", "productInfo": "Thông tin sản phẩm", "productAttributes": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> sản ph<PERSON>m", "productImages": "<PERSON><PERSON><PERSON> sản phẩm", "priceType": {"title": "Loại giá", "hasPrice": "<PERSON><PERSON> giá cố định", "stringPrice": "<PERSON><PERSON><PERSON> dạng mô tả", "noPrice": "Không có giá"}, "status": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON> bán", "inactive": "<PERSON><PERSON><PERSON> b<PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON>"}, "form": {"title": "<PERSON><PERSON><PERSON><PERSON> sản phẩm vật lý", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "price": "Giá", "category": "<PERSON><PERSON>", "sku": "Mã SKU", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "inventory": "Số lư<PERSON>ng tồn kho", "tags": "Thẻ", "typePrice": "Loại giá", "images": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancel": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON><PERSON> sản phẩm mới", "editTitle": "Chỉnh s<PERSON>a sản phẩm", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên sản phẩm", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả sản phẩm", "pricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "categoryPlaceholder": "<PERSON><PERSON><PERSON> danh mục sản phẩm", "skuPlaceholder": "<PERSON><PERSON>ậ<PERSON> mã SKU sản phẩm", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái sản phẩm", "inventoryPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON> tồn kho", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ sản phẩm", "mediaPlaceholder": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm", "media": "Ảnh sản phẩm", "shipmentConfig": {"title": "<PERSON><PERSON><PERSON> hình vận chuyển", "widthCm": "<PERSON><PERSON>u rộng (cm)", "heightCm": "<PERSON><PERSON><PERSON> cao (cm)", "lengthCm": "<PERSON><PERSON><PERSON> dài (cm)"}, "customFields": {"title": "Trường tùy chỉnh", "selectField": "<PERSON><PERSON>n trường tùy chỉnh", "selectGroupForm": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh", "searchPlaceholder": "T<PERSON>m kiếm trường tùy chỉnh...", "searchGroupPlaceholder": "Tìm kiếm nhóm trường tùy chỉnh...", "selectedFields": "Trường tùy chỉnh đã chọn", "selectedGroupForm": "Nhóm trường tùy chỉnh đã chọn", "addField": "Thêm trường tùy chỉnh", "addGroupForm": "Thêm nhóm trường tùy chỉnh"}, "variants": {"title": "Phân loại mẫu mã", "addVariant": "<PERSON>hê<PERSON> phân lo<PERSON>i", "variant": "<PERSON><PERSON> lo<PERSON>", "noVariants": "<PERSON><PERSON><PERSON> có phân loại nào. Nhấn \"Thêm phân loại\" để bắt đầu.", "customFields": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h phân lo<PERSON>i", "searchCustomField": "<PERSON><PERSON><PERSON>"}, "priceDescriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả giá", "priceTypePlaceholder": "<PERSON><PERSON>n lo<PERSON>i giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}}, "customFields": {"title": "Trường tùy chỉnh", "selectField": "<PERSON><PERSON>n trường tùy chỉnh", "selectGroupForm": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh", "searchPlaceholder": "T<PERSON>m kiếm trường tùy chỉnh...", "searchGroupPlaceholder": "Tìm kiếm nhóm trường tùy chỉnh...", "selectedFields": "Trường tùy chỉnh đã chọn", "selectedGroupForm": "Nhóm trường tùy chỉnh đã chọn", "addField": "Thêm trường tùy chỉnh", "addGroupForm": "Thêm nhóm trường tùy chỉnh"}, "fields": {"name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "Giá", "priceType": "Loại giá", "priceTypes": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "regularPrice": "<PERSON><PERSON><PERSON>", "salePrice": "<PERSON><PERSON><PERSON> mãi", "priceNote": "<PERSON><PERSON> chú về giá", "brand": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "description": "<PERSON><PERSON>", "attributes": "<PERSON><PERSON><PERSON><PERSON>", "attributeName": "<PERSON><PERSON><PERSON>", "attributeType": "<PERSON><PERSON><PERSON> dữ liệu", "attributeValue": "<PERSON><PERSON><PERSON> trị mặc định"}, "attributeTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "date": "<PERSON><PERSON><PERSON>", "boolean": "Có/<PERSON>hông", "list": "<PERSON><PERSON>"}, "images": {"addImages": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh sản phẩm", "image": "Ảnh", "url": "URL", "video": "Video", "uploadImage": "<PERSON><PERSON><PERSON>", "enterImageUrl": "Nhập URL hình ảnh", "enterVideoUrl": "Nhập URL video", "recommendedSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> nghị: 800x600px, tối đa 2MB", "addToList": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o danh s<PERSON>ch", "uploadedImages": "<PERSON><PERSON> t<PERSON> lên", "urlImages": "<PERSON><PERSON> s<PERSON> từ URL", "videoList": "<PERSON><PERSON> video", "setCover": "Đặt làm ảnh bìa", "coverImage": "Ảnh bìa", "uploadedFromComputer": "<PERSON><PERSON><PERSON> lên từ máy t<PERSON>h", "dragAndDrop": "<PERSON><PERSON>o thả hoặc click để tải lên ảnh sản phẩm"}, "actions": {"createProduct": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "saveProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "view": "Xem", "deleteProduct": "<PERSON><PERSON><PERSON> p<PERSON>m", "cancelCreation": "<PERSON><PERSON><PERSON>"}, "messages": {"productCreated": "<PERSON><PERSON><PERSON> sản phẩm thành công", "productUpdated": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "productDeleted": "<PERSON><PERSON><PERSON> sản phẩm thành công", "confirmDelete": "Bạn có chắc chắn muốn xóa sản phẩm này không?"}, "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "priceInfo": "Thông tin giá", "noImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "shipmentConfig": "<PERSON><PERSON><PERSON> hình vận chuyển", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "height": "<PERSON><PERSON><PERSON> cao", "length": "<PERSON><PERSON><PERSON> dài", "weight": "Cân nặng", "noTags": "<PERSON><PERSON><PERSON>ng có thẻ", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "detail": "<PERSON> tiết sản phẩm", "bulkUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "bulkUpdateSuccessMessage": "<PERSON><PERSON> cập nhật trạng thái cho {{count}} s<PERSON>n phẩm", "bulkUpdateError": "Lỗi cập nhật trạng thái", "bulkUpdateErrorMessage": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái sản phẩm. <PERSON><PERSON> lòng thử lại.", "bulkUpdateStatus": "<PERSON><PERSON><PERSON> nhật trạng thái nhiều sản phẩm", "confirmBulkStatusUpdateMessage": "Chọn trạng thái mới cho {{count}} sản phẩm đã chọn:", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "rejectReason": "<PERSON>ý do từ chối", "rejectReasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lý do từ chối...", "statusTypes": {"APPROVED": "<PERSON><PERSON> p<PERSON>", "PENDING": "<PERSON><PERSON> phê <PERSON>", "REJECTED": "Đ<PERSON> từ chối"}, "viewForm": {"status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "priceTypes": {"yes": "<PERSON>ó gi<PERSON>", "no": "<PERSON><PERSON><PERSON> phí", "other": "<PERSON><PERSON><PERSON>"}, "imagesTitle": "<PERSON><PERSON><PERSON> sản phẩm", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "close": "Đ<PERSON><PERSON>", "view": "Xem"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "table": {"image": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "<PERSON><PERSON><PERSON> b<PERSON>", "typePrice": "Loại giá", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động", "selectedItems": "<PERSON><PERSON> chọn {{count}} mục", "clearSelection": "Bỏ chọn tất cả"}, "update": "<PERSON><PERSON><PERSON>", "loadError": "<PERSON><PERSON> lỗi xảy ra khi tải dữ liệu sản phẩm"}, "customField": {"title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh", "maxLength": "<PERSON><PERSON> dài tối đa", "totalFields": "Tổng số trường tùy chỉnh", "manage": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh", "add": "Thêm trường tùy chỉnh", "edit": "Chỉnh sửa trường tùy chỉnh", "addForm": "Thêm trường tùy chỉnh mới", "editForm": "Chỉnh sửa trường tùy chỉnh", "component": "<PERSON><PERSON><PERSON> thành phần", "components": {"input": "Ô nhập liệu", "textarea": "Ô văn bản", "select": "<PERSON><PERSON>n", "checkbox": "<PERSON><PERSON><PERSON>", "radio": "Nút radio", "date": "<PERSON><PERSON><PERSON>", "number": "Số", "file": "<PERSON><PERSON>p tin", "multiSelect": "<PERSON><PERSON><PERSON>"}, "type": "<PERSON><PERSON><PERSON> dữ liệu", "configId": "<PERSON> c<PERSON>u hình", "type.string": "<PERSON><PERSON><PERSON>", "type.number": "Số", "type.boolean": "Có/<PERSON>hông", "type.date": "<PERSON><PERSON><PERSON>", "type.object": "<PERSON><PERSON><PERSON>", "type.array": "<PERSON><PERSON><PERSON>", "types": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "boolean": "Có/<PERSON>hông", "date": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "object": "<PERSON><PERSON><PERSON>", "array": "<PERSON><PERSON><PERSON>", "string": "<PERSON><PERSON><PERSON>"}, "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>", "validation": {"minLength": "<PERSON><PERSON> dài tối thiểu", "maxLength": "<PERSON><PERSON> dài tối đa", "pattern": "Mẫu kiểm tra", "min": "<PERSON><PERSON><PERSON> trị tối thiểu", "max": "<PERSON><PERSON><PERSON> trị tối đa"}, "configuration": "<PERSON><PERSON><PERSON> h<PERSON>nh", "value": "<PERSON><PERSON><PERSON> trị", "fieldTypes": {"text": "<PERSON><PERSON><PERSON>", "number": "Số", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "date": "<PERSON><PERSON><PERSON>", "boolean": "Đúng/Sai", "select": "<PERSON><PERSON><PERSON>", "textarea": "<PERSON><PERSON><PERSON> bản dài"}, "form": {"componentRequired": "<PERSON><PERSON> lòng chọn loại thành phần", "labelRequired": "<PERSON><PERSON> lòng nh<PERSON><PERSON> nh<PERSON>n", "typeRequired": "<PERSON><PERSON> lòng chọn kiểu dữ liệu", "idRequired": "<PERSON><PERSON> lòng nhập tên trường định danh", "configIdRequired": "ID cấu hình là bắt buộc", "configIdPlaceholder": "<PERSON>hập <PERSON> c<PERSON>u hình", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n hiển thị", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho trường này", "placeholderPlaceholder": "<PERSON>h<PERSON>p placeholder", "defaultValuePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá trị mặc định", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> các tù<PERSON>, ph<PERSON> cách bằng dấu phẩy hoặc định dạng JSON", "selectOptionsPlaceholder": "<PERSON>h<PERSON>p giá trị theo cấu trúc: Name|Value, Mỗi cặp giá trị trên 1 dòng. VD:\na|1\nb|2", "booleanDefaultPlaceholder": "<PERSON><PERSON><PERSON> giá trị mặc định", "dateDefaultPlaceholder": "<PERSON><PERSON><PERSON> ngày mặc định", "labelTagRequired": "<PERSON><PERSON> lòng thêm ít nhất một nhãn", "fieldIdLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>nh danh", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> hiển thị", "displayNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị cho trường này", "displayNameRequired": "<PERSON><PERSON> lòng nhập tên trường hiển thị", "labelInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n và nhấn Enter", "tagsCount": "nh<PERSON>n đã thêm", "patternSuggestions": "Gợi ý pattern phổ biến:", "showAdvancedSettings": "<PERSON><PERSON>n thị cài đặt nâng cao", "description": "<PERSON><PERSON>", "placeholder": "Placeholder", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "options": "<PERSON><PERSON><PERSON>"}, "createSuccess": "Tạo trường tùy chỉnh thành công", "createError": "Lỗi khi tạo trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật trường tùy chỉnh", "deleteSuccess": "<PERSON>óa trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa trường tùy chỉnh", "loadError": "Lỗi khi tải trường tùy chỉnh", "booleanValues": {"true": "<PERSON><PERSON>", "false": "K<PERSON>ô<PERSON>"}, "patterns": {"email": "Email", "phoneVN": "Số điện thoại VN", "phoneIntl": "<PERSON><PERSON> điện thoại quốc tế", "postalCodeVN": "<PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> VN", "lettersOnly": "Chỉ chữ cái", "numbersOnly": "Chỉ số", "alphanumeric": "Chữ và số", "noSpecialChars": "<PERSON><PERSON><PERSON><PERSON> có ký tự đặc biệt", "url": "URL", "ipv4": "IPv4", "strongPassword": "<PERSON><PERSON><PERSON><PERSON> mạnh", "vietnameseName": "<PERSON><PERSON><PERSON> (c<PERSON>)", "studentId": "<PERSON><PERSON> sinh viên", "nationalId": "CMND/CCCD", "taxCode": "<PERSON><PERSON> số thuế", "dateFormat": "<PERSON>ày (dd/mm/yyyy)", "timeFormat": "Giờ (hh:mm)", "hexColor": "Hex color", "base64": "Base64", "uuid": "UUID", "filename": "Tên file", "urlSlug": "Slug URL", "variableName": "<PERSON><PERSON><PERSON>", "creditCard": "Số thẻ tín dụng", "qrCode": "Mã QR", "gpsCoordinate": "Tọa độ GPS", "rgbColor": "Mã màu RGB", "domain": "<PERSON><PERSON><PERSON>", "decimal": "<PERSON><PERSON> thập phân", "barcode": "Mã vạch"}, "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa trường tùy chỉnh này?", "table": {"name": "<PERSON><PERSON><PERSON> tr<PERSON>", "component": "<PERSON><PERSON><PERSON><PERSON> phần", "type": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "status": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "pending": "<PERSON><PERSON> lý", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "required": {"required": "<PERSON><PERSON><PERSON> b<PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}, "confirmDelete": {"title": "<PERSON><PERSON><PERSON>n x<PERSON>a"}, "detail": "<PERSON> tiết trường tùy chỉnh", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trường tùy chỉnh", "configJson": "<PERSON><PERSON><PERSON> h<PERSON>nh JSO<PERSON>", "noConfigJson": "<PERSON><PERSON><PERSON><PERSON> có cấu hình JSON", "linkedGroups": "<PERSON><PERSON><PERSON><PERSON> liên k<PERSON>t", "createdAt": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "routes": {"business": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h do<PERSON> - <PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm - <PERSON><PERSON>", "conversion": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi - <PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng - <PERSON><PERSON>", "warehouse": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> - <PERSON><PERSON>", "virtualWarehouse": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> - <PERSON><PERSON>", "customField": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh - Ad<PERSON>", "warehouseCustomField": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh kho - Admin", "file": "<PERSON><PERSON><PERSON><PERSON> lý file - <PERSON><PERSON>", "folder": "<PERSON><PERSON><PERSON><PERSON> lý thư mụ<PERSON> - <PERSON><PERSON>"}, "businessPage": {"modules": {"product": {"title": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "description": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi sản phẩm của người dùng"}, "conversion": {"title": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi", "description": "<PERSON> và quản lý các bản ghi chuyển đổi"}, "order": {"title": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý và theo dõi đơn hàng của người dùng"}, "warehouse": {"title": "<PERSON><PERSON><PERSON><PERSON> lý kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho vật lý và kho ảo của người dùng"}, "virtualWarehouse": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho <PERSON>o và tích hợp hệ thống"}, "customField": {"title": "Trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của hệ thống"}, "warehouseCustomField": {"title": "Trường tùy chỉnh kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý các trường tùy chỉnh của kho"}, "file": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> file", "description": "<PERSON><PERSON><PERSON><PERSON> lý file và tài liệu của hệ thống"}, "folder": {"title": "<PERSON><PERSON><PERSON><PERSON> lý thư mục", "description": "<PERSON><PERSON><PERSON><PERSON> lý cấu trúc thư mục của hệ thống"}}}, "virtualWarehouse": {"title": "<PERSON><PERSON> <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý c<PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "warehouseId": "ID kho", "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON>", "warehouse": "Thông tin kho", "customFields": "Trường tùy chỉnh", "name": "<PERSON><PERSON><PERSON> kho", "type": "<PERSON><PERSON><PERSON> kho", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "createError": "Lỗi khi tạo kho <PERSON>o", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t kho <PERSON>o thành công", "updateError": "Lỗi khi cập nh<PERSON>t kho <PERSON>o", "deleteSuccess": "<PERSON><PERSON><PERSON> kho <PERSON>o thành công", "deleteError": "Lỗi khi xóa kho ảo", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho <PERSON>o nà<PERSON>?", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kho <PERSON>o", "detail": "<PERSON> ti<PERSON>t kho <PERSON>o", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "warehouseInfo": "Thông tin kho", "customFieldsInfo": "Trường tùy chỉnh", "noCustomFields": "<PERSON><PERSON><PERSON>ng có trường tùy chỉnh", "noAssociatedSystem": "<PERSON><PERSON><PERSON><PERSON> có hệ thống liên kết", "noPurpose": "<PERSON><PERSON><PERSON><PERSON> có mục đ<PERSON>ch", "close": "Đ<PERSON><PERSON>", "table": {"warehouseId": "ID kho", "name": "<PERSON><PERSON><PERSON> kho", "associatedSystem": "<PERSON><PERSON> thống liên kết", "purpose": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "types": {"VIRTUAL": "<PERSON><PERSON> <PERSON><PERSON>", "PHYSICAL": "<PERSON><PERSON> v<PERSON> lý"}, "statusTypes": {"ACTIVE": "<PERSON><PERSON><PERSON> đ<PERSON>", "INACTIVE": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}}, "customGroupForm": {"title": "Nhóm trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhóm trường tùy chỉnh", "createSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "createError": "Lỗi khi tạo nhóm trường tùy chỉnh", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhóm trường tùy chỉnh thành công", "updateError": "Lỗi khi cập nhật nhóm trường tùy chỉnh", "deleteSuccess": "<PERSON><PERSON><PERSON> nhóm trường tùy chỉnh thành công", "deleteError": "Lỗi khi xóa nhóm trường tùy chỉnh", "loadError": "Lỗi khi tải nhóm trường tùy chỉnh"}, "order": {"title": "<PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng", "createOrder": "<PERSON><PERSON><PERSON> đơn hàng mới", "editOrder": "Chỉnh sửa đơn hàng", "viewOrder": "<PERSON>em chi tiết đơn hàng", "orderNumber": "<PERSON><PERSON> đơn hàng", "customerInfo": "Thông tin khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "customerEmail": "Email", "customerPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "customerAddress": "Địa chỉ", "items": "<PERSON><PERSON><PERSON> phẩm trong đơn hàng", "noItems": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong đơn hàng", "quantity": "Số lượng", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "status": {"title": "<PERSON><PERSON><PERSON><PERSON> thái", "pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "refunded": "<PERSON><PERSON> hoàn tiền"}, "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "paymentMethods": {"cash": "Tiền mặt", "creditCard": "Thẻ tín dụng", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "digitalWallet": "<PERSON><PERSON> điện tử"}, "paymentStatus": {"title": "<PERSON>r<PERSON><PERSON> thái thanh toán", "paid": "<PERSON><PERSON> thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "partiallyPaid": "<PERSON><PERSON> <PERSON><PERSON> một ph<PERSON>n"}, "notes": "<PERSON><PERSON><PERSON>", "form": {"customerNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng", "customerEmailPlaceholder": "Nhập email kh<PERSON>ch hàng", "customerPhonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện tho<PERSON><PERSON> kh<PERSON>ch hàng", "customerAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ khách hàng", "notesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho đơn hàng"}, "createSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "createError": "Lỗi khi tạo đơn hàng", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công", "updateError": "Lỗi khi cập nhật đơn hàng", "deleteSuccess": "<PERSON><PERSON><PERSON> đơn hàng thành công", "deleteError": "Lỗi khi xóa đơn hàng", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa đơn hàng này?", "table": {"orderNumber": "<PERSON><PERSON> đơn hàng", "customer": "<PERSON><PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON> tiền", "carrier": "Đơn vị vận chuyển", "orderDate": "<PERSON><PERSON><PERSON> đặt hàng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"view": "Xem"}, "detail": "<PERSON> tiết đơn hàng", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn hàng", "source": "<PERSON><PERSON><PERSON><PERSON>", "shippingStatus": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "hasShipping": "<PERSON><PERSON> vận chuy<PERSON>n", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "productInfo": "Thông tin sản phẩm", "billInfo": "<PERSON>h<PERSON><PERSON> tin hóa đơn", "subtotal": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> cộng", "logisticInfo": "Thông tin vận chuyển", "carrier": "Đơn vị vận chuyển", "address": "Địa chỉ", "notSet": "Chưa đặt", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "close": "Đ<PERSON><PERSON>", "shipped": "Đ<PERSON> gửi", "delivered": "Đã giao"}, "conversion": {"title": "<PERSON><PERSON><PERSON><PERSON> đổi", "description": "<PERSON> và quản lý các chuyển đổi", "totalConversions": "Tổng số chuyển đổi", "manage": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi", "id": "ID", "customerId": "ID khách hàng", "userId": "ID người dùng", "type": "<PERSON><PERSON><PERSON> chuyển đổi", "name": "<PERSON><PERSON><PERSON>", "source": "<PERSON><PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON> trị", "date": "<PERSON><PERSON><PERSON>", "status": {"completed": "<PERSON><PERSON><PERSON> th<PERSON>", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "detail": "<PERSON> tiết chuyển đổi", "conversionInfo": "Th<PERSON>ng tin chuyển đổi", "customerInfo": "Thông tin khách hàng", "conversionType": "<PERSON><PERSON><PERSON> chuyển đổi", "notes": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON>i dung", "campaign": "<PERSON><PERSON><PERSON>", "avatar": "Ảnh đại diện", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "timezone": "<PERSON><PERSON><PERSON> giờ", "agentId": "ID Agent", "metadata": "<PERSON><PERSON> li<PERSON><PERSON> b<PERSON> sung", "tags": "Thẻ", "primaryEmail": "<PERSON><PERSON>", "secondaryEmail": "<PERSON><PERSON> phụ", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin chuyển đổi", "table": {"id": "ID", "customerId": "ID Khách h<PERSON>ng", "userId": "ID Người dùng", "type": "<PERSON><PERSON><PERSON> chuyển đổi", "source": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"view": "Xem"}, "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "close": "Đ<PERSON><PERSON>"}, "customer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin khách hàng", "totalCustomers": "Tổng số khách hàng", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "blocked": "Đã chặn"}}, "report": {"title": "Báo cáo", "description": "<PERSON><PERSON> c<PERSON>c báo cáo kinh doanh", "totalReports": "Tổng số báo cáo", "view": "<PERSON><PERSON> b<PERSON>o c<PERSON>o"}, "inventory": {"title": "<PERSON><PERSON><PERSON><PERSON> lý kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý hàng tồn kho và nhập xuất kho", "totalItems": "Tổng số mặt hàng", "manage": "<PERSON><PERSON><PERSON><PERSON> lý kho", "status": {"inStock": "<PERSON><PERSON><PERSON> hàng", "lowStock": "<PERSON><PERSON><PERSON>", "outOfStock": "<PERSON><PERSON><PERSON>"}}, "file": {"detail": "<PERSON> tiế<PERSON> file", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy file", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "Tên file", "originalName": "<PERSON><PERSON><PERSON>", "type": "Loại file", "size": "<PERSON><PERSON><PERSON>", "extension": "Phần mở rộng", "mimeType": "MIME Type", "folderInfo": "<PERSON><PERSON><PERSON><PERSON> tin thư mục", "urls": "Đường dẫn", "url": "URL", "thumbnailUrl": "URL thumbnail", "metadata": "<PERSON><PERSON><PERSON>", "noMetadata": "Không có metadata", "storageKey": "Storage Key", "types": {"image": "<PERSON><PERSON><PERSON>", "video": "Video", "document": "<PERSON><PERSON><PERSON> l<PERSON>", "audio": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "table": {"name": "Tên file", "size": "<PERSON><PERSON><PERSON>", "folder": "<PERSON><PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "actions": {"view": "Xem"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "image": "<PERSON><PERSON><PERSON>", "video": "Video", "document": "<PERSON><PERSON><PERSON> l<PERSON>", "audio": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "loadError": "<PERSON><PERSON> lỗi xảy ra khi tải dữ liệu file", "notSet": "Chưa đặt"}, "folder": {"detail": "<PERSON> tiết thư mục", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON> th<PERSON> mục", "parentId": "<PERSON> thư mục cha", "description": "<PERSON><PERSON>", "pathInfo": "Thông tin đường dẫn", "path": "Đường dẫn", "breadcrumbs": "Đường dẫn đầy đủ", "noBreadcrumbs": "<PERSON><PERSON><PERSON> m<PERSON>c", "parentInfo": "<PERSON><PERSON><PERSON><PERSON> tin thư mục cha", "parentName": "<PERSON><PERSON><PERSON> thư mục cha", "parentPath": "Đường dẫn thư mục cha", "ownerInfo": "Thông tin người sở hữu", "ownerId": "ID người sở hữu", "ownerName": "<PERSON><PERSON><PERSON> ng<PERSON>ời sở hữu", "ownerEmail": "<PERSON><PERSON> sở hữu", "fileCount": "S<PERSON> lư<PERSON> file", "subFolderCount": "<PERSON><PERSON> l<PERSON><PERSON> thư mục con", "files": "file(s)", "subFolders": "th<PERSON> mục con", "root": "Root", "filesList": "<PERSON><PERSON> s<PERSON> files", "filesInFolder": "Files trong thư mục này sẽ được hiển thị ở đây", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "table": {"name": "<PERSON><PERSON><PERSON> th<PERSON> mục", "parent": "<PERSON><PERSON><PERSON> m<PERSON> cha", "path": "Đường dẫn", "owner": "Người sở hữu", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "actions": {"view": "<PERSON>em chi tiết"}, "rootFolder": "<PERSON><PERSON><PERSON> m<PERSON>c", "unknownOwner": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "loadError": "<PERSON><PERSON> lỗi xảy ra khi tải dữ liệu thư mục"}, "warehouse": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho hàng", "name": "<PERSON><PERSON><PERSON> kho", "code": "<PERSON><PERSON> kho", "desc": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON> kho", "types": {"PHYSICAL": "<PERSON><PERSON> v<PERSON> lý", "VIRTUAL": "<PERSON><PERSON> <PERSON><PERSON>"}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "address": "Địa chỉ", "contact": "<PERSON>h<PERSON>ng tin liên hệ", "add": "<PERSON><PERSON><PERSON><PERSON> kho", "edit": "Chỉnh sửa kho", "addForm": "<PERSON><PERSON><PERSON><PERSON> kho mới", "editForm": "Chỉnh sửa thông tin kho", "createSuccess": "<PERSON><PERSON><PERSON> kho thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật kho thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> kho thành công", "createError": "Lỗi khi tạo kho", "updateError": "Lỗi khi cập nhật kho", "deleteError": "Lỗi khi xóa kho", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa kho này không?", "form": {"namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên kho", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả kho", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON> kho", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON> kho"}, "detail": "<PERSON> tiết kho", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kho", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "id": "ID", "capacity": "<PERSON><PERSON><PERSON>", "customFields": "Trường tùy chỉnh", "noCustomFields": "<PERSON><PERSON><PERSON>ng có trường tùy chỉnh", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả", "notSet": "Chưa đặt", "close": "Đ<PERSON><PERSON>", "table": {"id": "ID", "name": "<PERSON><PERSON><PERSON> kho", "description": "<PERSON><PERSON>", "address": "Địa chỉ", "capacity": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "highCapacity": "<PERSON><PERSON><PERSON> ch<PERSON> cao", "lowCapacity": "<PERSON><PERSON><PERSON> ch<PERSON>a thấp"}, "actions": {"view": "Xem"}}, "warehouseCustomField": {"title": "Trường tùy chỉnh kho", "description": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh của kho", "detail": "<PERSON> tiết trường tùy chỉnh kho", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "fieldInfo": "Thông tin trường", "currentValue": "<PERSON><PERSON><PERSON> trị hiện tại", "warehouseId": "ID kho", "fieldId": "ID trường", "warehouseName": "<PERSON><PERSON><PERSON> kho", "fieldLabel": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON>", "notFound": "<PERSON><PERSON>ông tìm thấy thông tin trường tùy chỉnh", "bulkUpdateStatus": "<PERSON><PERSON><PERSON> nhật trạng thái", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "notSet": "Chưa đặt", "close": "Đ<PERSON><PERSON>", "loadError": "<PERSON><PERSON> lỗi xảy ra khi tải dữ liệu trường tùy chỉnh kho", "table": {"id": "ID", "name": "<PERSON><PERSON><PERSON> tr<PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON>", "value": "<PERSON><PERSON><PERSON> trị", "warehouseId": "ID Kho", "actions": "<PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "filters": {"all": "<PERSON><PERSON><PERSON> cả kho", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "number": "<PERSON>r<PERSON><PERSON><PERSON> số", "boolean": "Trư<PERSON>ng boolean", "date": "<PERSON><PERSON><PERSON><PERSON><PERSON>y"}, "actions": {"view": "Xem"}}, "common": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "create": "<PERSON><PERSON><PERSON> mới", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "submit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON><PERSON>p", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Xóa", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "view": "Xem", "details": "<PERSON> ti<PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "enter": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "and": "và", "pressEnter": "<PERSON><PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "timeInfo": "<PERSON>h<PERSON>ng tin thời gian", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "close": "Đ<PERSON><PERSON>", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "<PERSON><PERSON> lý"}}}}}